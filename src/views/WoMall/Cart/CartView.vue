<template>
  <MainLayout>
    <!-- 未登录状态 -->
    <CartEmptyState v-if="!isLogin" type="login" @login="handleLogin" />

    <!-- 已登录状态 -->
    <section v-else class="cart">
      <!-- 地址选择区域 -->
      <CartHeader :address-display="addressDisplay" :is-edit-mode="isEditMode" @select-address="handleSelectAddress"
        @toggle-edit="handleEditToggle" />

      <!-- 主要内容区域 -->
      <main class="cart-main">
        <!-- 加载状态 -->
        <CartLoadingSkeleton v-if="isLoading" />

        <!-- 空购物车状态 -->
        <CartEmptyState v-else-if="isEmptyState" type="empty" @go-shopping="$router.push('/home')" />

        <!-- 购物车商品列表 -->
        <section v-else class="cart-goods">
          <!-- 有效商品 -->
          <div class="cart-goods__valid" v-for="(group, groupIndex) in validGoodsList" :key="groupIndex">
            <ValidGoodsItem v-for="(item, itemIndex) in group.goodsList" :key="item.cartSkuId" :item="item"
              :ref-key="`goodsItem_${groupIndex}_${itemIndex}`" :is-edit-mode="isEditMode"
              :is-edit-selected="isEditModeItemSelected(item)" @toggle-select="handleToggleItemSelect"
              @show-stepper="showStepper" @quantity-change="handleQuantityChange" @look-similar="handleLookSimilar"
              @delete-item="handleDeleteItem" @close-menu="closeLongPressMenu" @swipe-open="handleSwipeOpen"
              @swipe-close="handleSwipeClose" @set-ref="setGoodsItemRef" @long-press="handleLongPress"
              @gift-click="handleGiftClick" />
          </div>

          <!-- 失效商品 -->
          <WoCard v-if="hasInvalidGoods" class="cart-goods__invalid">
            <header class="cart-goods__invalid-header">
              <h3 class="cart-goods__invalid-title">{{ invalidGoodsCount }}件失效商品</h3>
              <button class="cart-goods__invalid-action" @click="handleClearInvalidGoods" type="button">
                一键清空
              </button>
            </header>
            <div class="cart-goods__invalid-list">
              <InvalidGoodsItem v-for="item in invalidGoodsList" :key="item.cartSkuId" :item="item"
                :invalid-count="invalidGoodsCount" @toggle-select="handleToggleItemSelect"
                @look-similar="handleLookSimilar" />
            </div>
          </WoCard>
        </section>
      </main>

      <!-- 底部结算栏占位符 -->
      <WoActionBarPlaceholder />
    </section>

    <!-- 底部结算栏 -->
    <WoActionBar :bottom="49" v-if="showFooter">
      <CartFooter :is-edit-mode="isEditMode" :edit-selected-count="tempSelectedItems.size"
        :is-edit-all-selected="isEditModeAllSelected" :selected-count="cartStore.selectCountAll"
        :total-price="cartStore.selectTotalPrice" :is-all-selected="cartStore.isSelectAll"
        :has-selected-goods="cartStore.hasSelectedGoods" @toggle-all-select="handleToggleAllSelect"
        @edit-toggle-all="handleEditModeToggleAll" @edit-delete="handleEditModeDelete" @checkout="handleCheckout" />
    </WoActionBar>
  </MainLayout>

  <!-- 弹窗组件 -->
  <GiftDisplayPopup v-model:visible="giftPopupVisible" :gift-list="giftList" :goods-num="currentGoodsNum" />
  <AddressQuickSelectionPopup v-model:visible="addressPopupVisible" @select="handleAddressSelect" />
</template>

<script setup>
import { ref, computed, onMounted, shallowRef, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { debounce, isEmpty } from 'lodash-es'
import { useNewCartStore, CART_QUERY_STATUS } from '@/store/modules/newCart'
import { useUserStore } from '@/store/modules/user'
import WoCard from '@components/WoElementCom/WoCard.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import GiftDisplayPopup from '@/components/Common/GiftDisplayPopup.vue'
import AddressQuickSelectionPopup from '@/components/Address/AddressQuickSelectionPopup.vue'
import ValidGoodsItem from '@views/WoMall/Cart/components/ValidGoodsItem.vue'
import InvalidGoodsItem from '@views/WoMall/Cart/components/InvalidGoodsItem.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import CartHeader from './components/CartHeader.vue'
import CartFooter from './components/CartFooter.vue'
import CartLoadingSkeleton from './components/CartLoadingSkeleton.vue'
import CartEmptyState from './components/CartEmptyState.vue'
import { getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/hooks/index.js'
import { similarity, getGiftDetails, checkOrderSku, jdAddressCheck } from '@/api/index.js'
import { BatchRequest } from '@/utils/tools.js'
import { buyProductCart, buyProductCartSession } from '@/utils/storage.js'

const $alert = useAlert()
const router = useRouter()
const cartStore = useNewCartStore()
const userStore = useUserStore()

const giftPopupVisible = ref(false)
const addressPopupVisible = ref(false)
const giftList = shallowRef([])
const currentGoodsNum = ref(0)
const isEditMode = ref(false)
const tempSelectedItems = shallowRef(new Set())
const longPressedItem = shallowRef(null)
const goodsItemRefs = shallowRef({})
const cacheSkuGiftDetailsList = markRaw(new Map())

const isLogin = computed(() => userStore.isLogin)
const addressDisplay = computed(() => {
  const addr = userStore.curAddressInfo
  if (!addr) return '请选择收货地址'
  return addr.addrDetail || `${addr.provinceName}${addr.cityName}${addr.countyName}`
})
const isLoading = computed(() => cartStore.getCartLoadingStatus === CART_QUERY_STATUS.LOADING)
const isEmptyState = computed(() =>
  !cartStore.hasValidGoods &&
  !cartStore.hasInvalidGoods &&
  cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS
)
const showFooter = computed(() =>
  cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS && cartStore.hasValidGoods
)
const validGoodsList = computed(() => cartStore.getCartValidList)
const invalidGoodsList = computed(() => cartStore.getCartInvalidList[0]?.goodsList || [])
const hasInvalidGoods = computed(() => cartStore.hasInvalidGoods)
const invalidGoodsCount = computed(() => invalidGoodsList.value.length)
const totalValidItems = computed(() =>
  validGoodsList.value.reduce((total, group) => total + group.goodsList.length, 0)
)
const isEditModeAllSelected = computed(() =>
  totalValidItems.value > 0 && tempSelectedItems.value.size === totalValidItems.value
)

const addressCheck = async () => {
  const [err, json] = await jdAddressCheck()
  if (err) {
    showToast(err.msg)
    return false
  }
  if (!json) {
    await $alert({
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存',
      confirmButtonText: '修改地址',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: () => {
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      },
      onCancelCallback: () => { }
    })
    return false
  }
  return true
}

const initializeGoodsProperties = () => {
  validGoodsList.value.forEach(group => {
    group.goodsList.forEach(item => {
      Object.assign(item, {
        stepperVisible: item.stepperVisible ?? false,
        isSwipeOpen: item.isSwipeOpen ?? false,
        showLongPressMenu: item.showLongPressMenu ?? false
      })
    })
  })
}

const initializeCart = async () => {
  try {
    await userStore.queryLoginStatus()

    if (userStore.isLogin) {
      showLoadingToast()
      const addressValid = await addressCheck()
      if (addressValid) {
        await cartStore.query()
        initializeGoodsProperties()
      }
      closeToast()
    }
  } catch (error) {
    closeToast()
    console.error('初始化购物车失败:', error)
  }
}

const debouncedAddressSelect = debounce(async () => {
  try {
    await initializeCart()
  } catch (error) {
    console.error('更新地址失败:', error)
    showToast('更新地址失败')
  }
}, 300)

const debouncedToggleAllSelect = debounce(async () => {
  try {
    await cartStore.checkedAllValid()
  } catch (error) {
    console.error('全选操作失败:', error)
  }
}, 300)

const debouncedQuantityChange = debounce(async (item) => {
  try {
    const error = await cartStore.updateGoodsNum({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId,
      goodsNum: item.skuNum
    })

    if (error) {
      if (error.code === 'FE2001' && error.stock !== undefined) {
        const targetItem = validGoodsList.value
          .flatMap(group => group.goodsList)
          .find(goods => goods.cartGoodsId === item.cartGoodsId && goods.cartSkuId === item.cartSkuId)

        if (targetItem) {
          targetItem.skuNum = error.stock
        }
      }
      showToast(error.msg || '更新商品数量失败')
    }
  } catch (error) {
    console.error('更新商品数量异常:', error)
    showToast('更新商品数量失败')
  }
}, 500)

const handleLogin = () => userStore.login()
const handleSelectAddress = () => {
  addressPopupVisible.value = true
}
const handleAddressSelect = () => debouncedAddressSelect()
const handleEditToggle = () => {
  if (isEditMode.value) {
    tempSelectedItems.value.clear()
  }
  isEditMode.value = !isEditMode.value
}
const handleToggleItemSelect = async (item) => {
  if (isEditMode.value) {
    handleEditModeSelect(item)
  } else {
    try {
      const newSelectState = item.selected !== 'true'
      await cartStore.updateGoodsSelectMuti([{
        goodsId: item.cartGoodsId,
        skuId: item.cartSkuId,
        select: newSelectState
      }])
    } catch (error) {
      console.error('更新商品选中状态失败:', error)
    }
  }
}
const handleToggleAllSelect = () => debouncedToggleAllSelect()
const showStepper = (item) => {
  item.stepperVisible = true
}
const handleQuantityChange = (item) => debouncedQuantityChange(item)
const handleDeleteItem = async (item) => {
  try {
    await cartStore.removeMuti(item)
  } catch (error) {
    console.error('删除商品失败:', error)
  }
}
const handleClearInvalidGoods = async () => {
  try {
    const deleteGoodsList = invalidGoodsList.value.map(item => ({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId
    }))
    await cartStore.removeInvalidGoods({ deleteGoodsList })
  } catch (error) {
    console.error('清空失效商品失败:', error)
  }
}
const handleSwipeOpen = (item) => {
  item.isSwipeOpen = true
}
const handleSwipeClose = (item) => {
  item.isSwipeOpen = false
}
const handleLongPress = (item) => {
  validGoodsList.value.forEach(group => {
    group.goodsList.forEach(good => {
      if (good !== item) {
        good.showLongPressMenu = false
      }
    })
  })

  longPressedItem.value = item
  item.showLongPressMenu = true
}
const closeLongPressMenu = () => {
  if (longPressedItem.value) {
    longPressedItem.value.showLongPressMenu = false
    longPressedItem.value = null
  }
}
const setGoodsItemRef = (el, key) => {
  goodsItemRefs.value[key] = el
}
const handleEditModeSelect = (item) => {
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  if (tempSelectedItems.value.has(itemId)) {
    tempSelectedItems.value.delete(itemId)
  } else {
    tempSelectedItems.value.add(itemId)
  }
}
const handleEditModeToggleAll = () => {
  if (tempSelectedItems.value.size === 0) {
    validGoodsList.value.forEach(group => {
      group.goodsList.forEach(item => {
        const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
        tempSelectedItems.value.add(itemId)
      })
    })
  } else {
    tempSelectedItems.value.clear()
  }
}
const handleEditModeDelete = async () => {
  if (tempSelectedItems.value.size === 0) return

  try {
    await $alert({
      title: '确认删除',
      message: `确定要删除选中的 ${tempSelectedItems.value.size} 件商品吗？`,
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        const deleteGoodsList = Array.from(tempSelectedItems.value).map(itemId => {
          const [goodsId, skuId] = itemId.split('_')
          return { goodsId, skuId }
        })

        await cartStore.removeMuti(deleteGoodsList)
        isEditMode.value = false
        tempSelectedItems.value.clear()
      }
    })
  } catch (error) {
    console.error('删除商品失败:', error)
  }
}
const isEditModeItemSelected = (item) => {
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  return tempSelectedItems.value.has(itemId)
}
const getGiftsDetails = async (goodsInfo, giftList) => {
  const skuId = goodsInfo.goods?.skuList?.[0]?.skuId
  const supplierCode = goodsInfo.goods?.supplierCode || 'jd_yg'

  if (!skuId || isEmpty(giftList)) return []

  if (cacheSkuGiftDetailsList.has(skuId)) {
    return cacheSkuGiftDetailsList.get(skuId)
  }

  const batchRequest = new BatchRequest()
  const tempGiftDetailsList = []

  giftList.forEach((item, index) => {
    const promise = getGiftDetails({
      supplierSkuId: item.giftId,
      supplierCode
    }).then(([err, json]) => {
      const giftDetail = err ? {} : {
        ...json,
        giftType: item.giftType,
        giftNum: item.giftNum,
        belongToSkuMaxNum: item.belongToSkuMaxNum,
        belongToSkuMinNum: item.belongToSkuMinNum
      }
      tempGiftDetailsList.splice(index, 0, giftDetail)
    })

    batchRequest.push(promise)
  })

  return new Promise((resolve) => {
    batchRequest.onComplete = () => {
      cacheSkuGiftDetailsList.set(skuId, tempGiftDetailsList)
      resolve(tempGiftDetailsList)
    }
  })
}
const handleGiftClick = async (data) => {
  const { goods: goodsInfo, giftList: goodsInfoGiftList } = data

  try {
    showLoadingToast()
    currentGoodsNum.value = goodsInfo.skuNum || 0

    const giftDetails = await getGiftsDetails(goodsInfo, goodsInfoGiftList)
    giftList.value = giftDetails.filter(item => !isEmpty(item))

    closeToast()
    giftPopupVisible.value = true
  } catch (error) {
    closeToast()
    console.error('获取赠品详情失败:', error)
    giftList.value = []
    currentGoodsNum.value = goodsInfo.skuNum || 0
    giftPopupVisible.value = true
  }
}
const handleLookSimilar = async (item) => {
  try {
    showLoadingToast()
    const goodsId = typeof item === 'string' ? item : item.cartGoodsId || item.goodsId
    const [err, json] = await similarity({
      goodsId,
      bizCode: getBizCode('GOODS')
    })

    closeToast()

    if (!err && json) {
      router.push(`/goodslist/${json}`)
    } else {
      router.push({ path: '/category' })
    }
  } catch (error) {
    closeToast()
    console.error('查看相似商品失败:', error)
    router.push({ path: '/category' })
  }
}
const handleCheckout = async () => {
  if (cartStore.selectCountAll < 1) {
    showToast('请选择下单商品')
    return
  }

  try {
    const buyGoodsList = validGoodsList.value
      .flatMap(group => group.goodsList)
      .filter(item => item.selected === 'true')
      .map(item => ({
        cartGoodsId: item.cartGoodsId,
        cartSkuId: item.cartSkuId,
        skuNum: item.skuNum,
        supplierCode: item.supplierCode,
        nowPrice: item.nowPrice
      }))

    if (isEmpty(buyGoodsList)) {
      showToast('请选择下单商品')
      return
    }

    buyProductCart.set(buyGoodsList)
    buyProductCartSession.set(buyGoodsList)

    const info = userStore.curAddressInfo
    if (!info) {
      showToast('请先选择收货地址')
      return
    }

    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    showLoadingToast()

    const [err] = await checkOrderSku({
      bizCode: getBizCode('ORDER'),
      addressInfo,
      buyGoodsList: JSON.stringify(buyGoodsList)
    })

    closeToast()

    if (!err) {
      router.push('/orderconfirm')
    } else {
      showToast(err.msg || '订单验证失败')

      setTimeout(async () => {
        try {
          showLoadingToast()
          await cartStore.query()
          closeToast()
        } catch (refreshError) {
          closeToast()
          console.error('刷新购物车失败:', refreshError)
        }
      }, 1500)
    }
  } catch (error) {
    closeToast()
    console.error('结算处理失败:', error)
    showToast('结算失败，请重试')
  }
}

onMounted(() => {
  initializeCart()
})
</script>
<style scoped lang="less">
// 购物车主容器
.cart {
  height: 100%;
  padding: 10px 10px 0 10px;
  box-sizing: border-box;
  background: #F8F9FA;
  user-select: none;
  overflow: auto;
  contain: layout style;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

// 购物车主要内容区域
.cart-main {
  flex: 1;
  overflow: hidden;
}

// 购物车商品列表样式
.cart-goods {
  &__valid {
    margin-bottom: 10px;
  }

  &__invalid {
    margin-top: 10px;
  }

  &__invalid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  &__invalid-title {
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin: 0;
  }

  &__invalid-action {
    font-size: @font-size-14;
    color: @theme-color;
    font-weight: @font-weight-600;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }
}
</style>
